#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const http = require('http');

console.log('🔍 企业画像师平台诊断工具');
console.log('================================');

// 检查文件结构
function checkFileStructure() {
  console.log('📁 检查项目文件结构...');
  
  const requiredFiles = [
    'package.json',
    'server/package.json',
    'server/src/app.ts',
    'server/.env',
    'src/App.tsx',
    'src/services/api.ts'
  ];
  
  const requiredDirs = [
    'server/src',
    'server/src/routes',
    'server/src/services',
    'server/src/config',
    'src/components'
  ];
  
  let allFilesExist = true;
  
  requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file}`);
    } else {
      console.log(`❌ ${file} - 文件缺失`);
      allFilesExist = false;
    }
  });
  
  requiredDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      console.log(`✅ ${dir}/`);
    } else {
      console.log(`❌ ${dir}/ - 目录缺失`);
      allFilesExist = false;
    }
  });
  
  return allFilesExist;
}

// 检查依赖安装
function checkDependencies() {
  console.log('\n📦 检查依赖安装...');
  
  const frontendNodeModules = fs.existsSync('node_modules');
  const backendNodeModules = fs.existsSync('server/node_modules');
  
  console.log(`前端依赖: ${frontendNodeModules ? '✅ 已安装' : '❌ 未安装'}`);
  console.log(`后端依赖: ${backendNodeModules ? '✅ 已安装' : '❌ 未安装'}`);
  
  return frontendNodeModules && backendNodeModules;
}

// 检查环境配置
function checkEnvironment() {
  console.log('\n🔧 检查环境配置...');
  
  if (!fs.existsSync('server/.env')) {
    console.log('❌ server/.env 文件不存在');
    return false;
  }
  
  const envContent = fs.readFileSync('server/.env', 'utf8');
  const requiredVars = ['PORT', 'DEEPSEEK_API_KEY', 'CORS_ORIGIN'];
  
  let allVarsPresent = true;
  requiredVars.forEach(varName => {
    if (envContent.includes(`${varName}=`)) {
      console.log(`✅ ${varName}`);
    } else {
      console.log(`❌ ${varName} - 环境变量缺失`);
      allVarsPresent = false;
    }
  });
  
  return allVarsPresent;
}

// 检查端口占用
function checkPorts() {
  console.log('\n🔌 检查端口状态...');
  
  return new Promise((resolve) => {
    const ports = [3001, 5173];
    const results = {};
    let completed = 0;
    
    ports.forEach(port => {
      const server = http.createServer();
      
      server.listen(port, () => {
        results[port] = '可用';
        server.close();
        completed++;
        if (completed === ports.length) {
          ports.forEach(p => {
            console.log(`端口 ${p}: ${results[p] || '占用'}`);
          });
          resolve(results);
        }
      });
      
      server.on('error', () => {
        results[port] = '占用';
        completed++;
        if (completed === ports.length) {
          ports.forEach(p => {
            console.log(`端口 ${p}: ${results[p] || '占用'}`);
          });
          resolve(results);
        }
      });
    });
  });
}

// 测试后端连接
function testBackendConnection() {
  console.log('\n🌐 测试后端连接...');
  
  return new Promise((resolve) => {
    const req = http.get('http://localhost:3001/health', (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log('✅ 后端服务正常运行');
          console.log(`   响应: ${data.substring(0, 100)}...`);
          resolve(true);
        } else {
          console.log(`❌ 后端服务响应异常: ${res.statusCode}`);
          resolve(false);
        }
      });
    });
    
    req.on('error', () => {
      console.log('❌ 无法连接到后端服务');
      resolve(false);
    });
    
    req.setTimeout(5000, () => {
      console.log('❌ 后端连接超时');
      req.destroy();
      resolve(false);
    });
  });
}

// 生成解决方案
function generateSolutions(checks) {
  console.log('\n💡 问题诊断和解决方案');
  console.log('================================');
  
  if (!checks.fileStructure) {
    console.log('🔧 文件结构问题:');
    console.log('   - 请确保所有必需文件都存在');
    console.log('   - 运行: git status 检查文件状态');
  }
  
  if (!checks.dependencies) {
    console.log('🔧 依赖安装问题:');
    console.log('   - 运行: npm install');
    console.log('   - 运行: cd server && npm install');
  }
  
  if (!checks.environment) {
    console.log('🔧 环境配置问题:');
    console.log('   - 复制环境文件: cp server/.env.example server/.env');
    console.log('   - 检查API密钥配置');
  }
  
  if (!checks.backend) {
    console.log('🔧 后端服务问题:');
    console.log('   - 启动后端: cd server && npm run dev');
    console.log('   - 检查日志: server/logs/combined.log');
    console.log('   - 确认端口3001未被占用');
  }
  
  console.log('\n🚀 推荐启动步骤:');
  console.log('1. npm install');
  console.log('2. cd server && npm install');
  console.log('3. cp server/.env.example server/.env');
  console.log('4. npm run dev');
  console.log('5. 访问 http://localhost:5173');
}

// 主诊断流程
async function runDiagnosis() {
  const checks = {
    fileStructure: checkFileStructure(),
    dependencies: checkDependencies(),
    environment: checkEnvironment()
  };
  
  await checkPorts();
  checks.backend = await testBackendConnection();
  
  console.log('\n📊 诊断结果总结');
  console.log('================================');
  Object.entries(checks).forEach(([key, value]) => {
    const status = value ? '✅ 正常' : '❌ 异常';
    const name = {
      fileStructure: '文件结构',
      dependencies: '依赖安装',
      environment: '环境配置',
      backend: '后端服务'
    }[key];
    console.log(`${name}: ${status}`);
  });
  
  const allGood = Object.values(checks).every(Boolean);
  if (allGood) {
    console.log('\n🎉 所有检查通过！系统应该可以正常运行。');
    console.log('如果仍有问题，请运行: node test-api.js');
  } else {
    generateSolutions(checks);
  }
}

runDiagnosis().catch(console.error);
