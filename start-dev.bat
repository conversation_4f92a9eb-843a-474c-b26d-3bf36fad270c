@echo off
echo 🚀 启动企业画像师平台开发环境
echo ================================

REM 检查Node.js版本
echo 📋 检查环境...
node -v >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Node.js，请先安装Node.js 18+
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node -v') do echo ✅ Node.js版本: %%i
)

REM 检查npm版本
npm -v >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到npm
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('npm -v') do echo ✅ npm版本: %%i
)

REM 安装前端依赖
echo.
echo 📦 安装前端依赖...
if not exist "node_modules" (
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 前端依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo ✅ 前端依赖已存在
)

REM 安装后端依赖
echo.
echo 📦 安装后端依赖...
cd server
if not exist "node_modules" (
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 后端依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo ✅ 后端依赖已存在
)
cd ..

REM 检查环境变量文件
echo.
echo 🔧 检查配置文件...
if not exist "server\.env" (
    echo ⚠️  未找到server\.env文件，从示例文件复制...
    copy "server\.env.example" "server\.env"
    echo ✅ 已创建server\.env文件，请根据需要修改配置
)

REM 创建日志目录
if not exist "server\logs" mkdir "server\logs"

echo.
echo 🗄️ 检查数据库设置...
echo 注意：首次运行需要启动数据库服务
echo MongoDB: mongodb://localhost:27017
echo Neo4j: bolt://localhost:7687 (用户名: neo4j, 密码: password)
echo PostgreSQL: localhost:5432 (用户名: postgres, 密码: password)
echo.
echo 如需初始化数据库，请运行: cd server && npm run db:setup
echo.
echo 🎯 启动开发服务器...
echo 前端地址: http://localhost:5174
echo 后端地址: http://localhost:3001
echo 健康检查: http://localhost:3001/health
echo.
echo 按 Ctrl+C 停止服务器
echo ================================

REM 启动开发服务器
npm run dev
