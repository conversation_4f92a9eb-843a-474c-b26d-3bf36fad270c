const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface Enterprise {
  id: string;
  name: string;
  industry: string;
  size?: string;
  stage?: string;
  location?: string;
  revenue?: string;
  employees?: number;
  founded?: number;
  status?: string;
  score?: number;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface EnterpriseQuery {
  page?: number;
  limit?: number;
  industry?: string;
  size?: string;
  stage?: string;
  location?: string;
  search?: string;
}

export interface AIAnalysisRequest {
  companyName: string;
  industry?: string;
  description?: string;
  targetConditions?: {
    industry?: string;
    size?: string;
    stage?: string;
    location?: string;
  };
}

class ApiService {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${API_BASE_URL}${endpoint}`;
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  // Health check
  async healthCheck(): Promise<ApiResponse> {
    return this.request('/health');
  }

  // Enterprise APIs
  async getEnterprises(query: EnterpriseQuery = {}): Promise<ApiResponse<{
    enterprises: Enterprise[];
    pagination: any;
  }>> {
    const searchParams = new URLSearchParams();
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, String(value));
      }
    });

    const queryString = searchParams.toString();
    return this.request(`/api/enterprises${queryString ? `?${queryString}` : ''}`);
  }

  async getEnterprise(id: string): Promise<ApiResponse<Enterprise>> {
    return this.request(`/api/enterprises/${id}`);
  }

  async createEnterprise(enterprise: Partial<Enterprise>): Promise<ApiResponse<Enterprise>> {
    return this.request('/api/enterprises', {
      method: 'POST',
      body: JSON.stringify(enterprise),
    });
  }

  async getEnterpriseStats(): Promise<ApiResponse> {
    return this.request('/api/enterprises/stats/overview');
  }

  // AI APIs
  async analyzeEnterprise(request: AIAnalysisRequest): Promise<ApiResponse> {
    return this.request('/api/ai/analyze-enterprise', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async generateMatching(enterprises: any[], targetConditions: any): Promise<ApiResponse> {
    return this.request('/api/ai/generate-matching', {
      method: 'POST',
      body: JSON.stringify({ enterprises, targetConditions }),
    });
  }

  async analyzePathway(enterpriseData: any, roleData: any): Promise<ApiResponse> {
    return this.request('/api/ai/analyze-pathway', {
      method: 'POST',
      body: JSON.stringify({ enterpriseData, roleData }),
    });
  }

  async getAIStatus(): Promise<ApiResponse> {
    return this.request('/api/ai/status');
  }

  // 智能分析APIs
  async performIntelligentAnalysis(data: {
    enterpriseId: string;
    analysisTypes: string[];
    includeCompetitors?: boolean;
    depth?: string;
  }): Promise<ApiResponse> {
    return this.request('/api/ai/intelligent-analysis', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getAnalysisHistory(enterpriseId: string, analysisType?: string): Promise<ApiResponse> {
    const params = new URLSearchParams();
    if (analysisType) params.append('analysisType', analysisType);

    return this.request(`/api/ai/analysis-history/${enterpriseId}${params.toString() ? `?${params.toString()}` : ''}`);
  }

  // 图像生成APIs
  async generateVisualization(data: {
    enterpriseName: string;
    industry: string;
    stage: string;
    analysisData?: any;
    visualizationType: string;
  }): Promise<ApiResponse> {
    return this.request('/api/ai/generate-visualization', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async generateBatchVisualization(data: {
    enterpriseName: string;
    industry: string;
    stage: string;
    analysisData?: any;
  }): Promise<ApiResponse> {
    return this.request('/api/ai/generate-batch-visualization', {
      method: 'POST',
      body: JSON.stringify({
        ...data,
        visualizationType: 'logo_concept' // 批量生成会忽略这个参数
      }),
    });
  }

  // Analysis APIs
  async createProfile(data: {
    targetConditions: any;
    searchMethods?: any;
  }): Promise<ApiResponse> {
    return this.request('/api/analysis/profile', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getEnterpriseAnalysis(id: string): Promise<ApiResponse> {
    return this.request(`/api/analysis/enterprise/${id}`);
  }

  async getRoleAnalysis(enterpriseId: string): Promise<ApiResponse> {
    return this.request(`/api/analysis/roles/${enterpriseId}`);
  }

  async getPathwayAnalysis(enterpriseId: string): Promise<ApiResponse> {
    return this.request(`/api/analysis/pathway/${enterpriseId}`);
  }
}

export const apiService = new ApiService();
