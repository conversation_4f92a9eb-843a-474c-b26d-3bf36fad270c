import { Router } from 'express';
import { asyncHandler } from '../middleware/errorHandler.js';
import { apiLogger } from '../utils/logger.js';
import Joi from 'joi';

const router = Router();

// Mock data for initial testing
const mockEnterprises = [
  {
    id: '1',
    name: '腾讯科技',
    industry: '互联网',
    size: '10000+人',
    stage: '成熟期',
    location: '深圳',
    revenue: '500亿+',
    employees: 85000,
    founded: 1998,
    status: '行业领导者',
    score: 95,
    description: '中国领先的互联网增值服务提供商',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: '2',
    name: '阿里巴巴',
    industry: '电子商务',
    size: '10000+人',
    stage: '成熟期',
    location: '杭州',
    revenue: '700亿+',
    employees: 120000,
    founded: 1999,
    status: '行业领导者',
    score: 98,
    description: '全球领先的电子商务和云计算公司',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: '3',
    name: '字节跳动',
    industry: '互联网',
    size: '10000+人',
    stage: '成长期',
    location: '北京',
    revenue: '300亿+',
    employees: 60000,
    founded: 2012,
    status: '快速成长',
    score: 92,
    description: '全球化的移动互联网公司',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Validation schemas
const enterpriseQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  industry: Joi.string().optional(),
  size: Joi.string().optional(),
  stage: Joi.string().optional(),
  location: Joi.string().optional(),
  search: Joi.string().optional()
});

const createEnterpriseSchema = Joi.object({
  name: Joi.string().required().min(1).max(200),
  industry: Joi.string().required().max(100),
  size: Joi.string().optional(),
  stage: Joi.string().optional(),
  location: Joi.string().optional(),
  revenue: Joi.string().optional(),
  employees: Joi.number().integer().min(0).optional(),
  founded: Joi.number().integer().min(1800).max(new Date().getFullYear()).optional(),
  description: Joi.string().optional().max(1000)
});

/**
 * @route GET /api/enterprises
 * @desc Get enterprises list with filtering and pagination
 * @access Public
 */
router.get('/', asyncHandler(async (req, res) => {
  apiLogger.info('Get enterprises request received', { query: req.query });

  // Validate query parameters
  const { error, value } = enterpriseQuerySchema.validate(req.query);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      details: error.details
    });
  }

  const { page, limit, industry, size, stage, location, search } = value;

  // Filter enterprises based on query parameters
  let filteredEnterprises = [...mockEnterprises];

  if (industry) {
    filteredEnterprises = filteredEnterprises.filter(e => 
      e.industry.toLowerCase().includes(industry.toLowerCase())
    );
  }

  if (size) {
    filteredEnterprises = filteredEnterprises.filter(e => e.size === size);
  }

  if (stage) {
    filteredEnterprises = filteredEnterprises.filter(e => e.stage === stage);
  }

  if (location) {
    filteredEnterprises = filteredEnterprises.filter(e => 
      e.location.toLowerCase().includes(location.toLowerCase())
    );
  }

  if (search) {
    filteredEnterprises = filteredEnterprises.filter(e => 
      e.name.toLowerCase().includes(search.toLowerCase()) ||
      e.description.toLowerCase().includes(search.toLowerCase())
    );
  }

  // Pagination
  const total = filteredEnterprises.length;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedEnterprises = filteredEnterprises.slice(startIndex, endIndex);

  res.json({
    success: true,
    data: {
      enterprises: paginatedEnterprises,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNext: endIndex < total,
        hasPrev: page > 1
      }
    }
  });
}));

/**
 * @route GET /api/enterprises/:id
 * @desc Get single enterprise by ID
 * @access Public
 */
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  apiLogger.info('Get enterprise by ID request received', { id });

  const enterprise = mockEnterprises.find(e => e.id === id);

  if (!enterprise) {
    return res.status(404).json({
      success: false,
      message: 'Enterprise not found'
    });
  }

  res.json({
    success: true,
    data: enterprise
  });
}));

/**
 * @route POST /api/enterprises
 * @desc Create new enterprise
 * @access Public
 */
router.post('/', asyncHandler(async (req, res) => {
  apiLogger.info('Create enterprise request received', { body: req.body });

  // Validate request
  const { error, value } = createEnterpriseSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      details: error.details
    });
  }

  // Create new enterprise (mock implementation)
  const newEnterprise = {
    id: String(mockEnterprises.length + 1),
    ...value,
    score: Math.floor(Math.random() * 30) + 70, // Random score between 70-100
    status: '待分析',
    createdAt: new Date(),
    updatedAt: new Date()
  };

  mockEnterprises.push(newEnterprise);

  res.status(201).json({
    success: true,
    data: newEnterprise,
    message: 'Enterprise created successfully'
  });
}));

/**
 * @route GET /api/enterprises/stats/overview
 * @desc Get enterprises statistics overview
 * @access Public
 */
router.get('/stats/overview', asyncHandler(async (req, res) => {
  apiLogger.info('Get enterprises stats request received');

  const stats = {
    total: mockEnterprises.length,
    byIndustry: mockEnterprises.reduce((acc, enterprise) => {
      acc[enterprise.industry] = (acc[enterprise.industry] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    byStage: mockEnterprises.reduce((acc, enterprise) => {
      acc[enterprise.stage] = (acc[enterprise.stage] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    byLocation: mockEnterprises.reduce((acc, enterprise) => {
      acc[enterprise.location] = (acc[enterprise.location] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    averageScore: mockEnterprises.reduce((sum, e) => sum + e.score, 0) / mockEnterprises.length
  };

  res.json({
    success: true,
    data: stats
  });
}));

export { router as enterpriseRoutes };
