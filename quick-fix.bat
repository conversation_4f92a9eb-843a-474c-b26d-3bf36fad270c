@echo off
echo 🔧 企业画像师平台快速修复工具
echo ================================

echo 📋 运行诊断...
node diagnose.js

echo.
echo 🔧 执行快速修复...

REM 确保环境文件存在
if not exist "server\.env" (
    echo 📄 创建环境配置文件...
    copy "server\.env.example" "server\.env"
    echo ✅ 已创建 server\.env
)

REM 创建必要目录
if not exist "server\logs" (
    echo 📁 创建日志目录...
    mkdir "server\logs"
    echo ✅ 已创建 server\logs
)

REM 检查并安装依赖
echo 📦 检查依赖安装...
if not exist "node_modules" (
    echo 安装前端依赖...
    npm install
)

cd server
if not exist "node_modules" (
    echo 安装后端依赖...
    npm install
)
cd ..

echo.
echo 🧪 测试后端连接...
timeout /t 2 /nobreak >nul
node test-api.js

echo.
echo ✅ 快速修复完成！
echo.
echo 💡 下一步:
echo 1. 运行 start-dev.bat 启动开发服务器
echo 2. 访问 http://localhost:5173 查看前端
echo 3. 访问 http://localhost:3001/health 检查后端
echo.
pause
