import axios from 'axios';
import { aiLogger } from '../utils/logger.js';
import { CustomError } from '../middleware/errorHandler.js';

export interface AIResponse {
  success: boolean;
  data?: any;
  error?: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface EnterpriseAnalysisRequest {
  companyName: string;
  industry?: string;
  description?: string;
  targetConditions?: {
    industry?: string;
    size?: string;
    stage?: string;
    location?: string;
  };
}

export class AIService {
  private deepseekApiUrl: string;
  private deepseekApiKey: string;
  private backupApiUrl: string;
  private backupApiKey: string;

  constructor() {
    this.deepseekApiUrl = process.env.DEEPSEEK_API_URL || 'https://api.deepseek.com';
    this.deepseekApiKey = process.env.DEEPSEEK_API_KEY || '';
    this.backupApiUrl = process.env.BACKUP_AI_API_URL || 'https://vip.apiyi.com/v1';
    this.backupApiKey = process.env.BACKUP_AI_API_KEY || '';

    if (!this.deepseekApiKey) {
      throw new Error('DEEPSEEK_API_KEY is required');
    }
  }

  /**
   * Call DeepSeek API with fallback to backup service
   */
  private async callAI(messages: any[], usePrimary: boolean = true): Promise<AIResponse> {
    const apiUrl = usePrimary ? this.deepseekApiUrl : this.backupApiUrl;
    const apiKey = usePrimary ? this.deepseekApiKey : this.backupApiKey;
    const model = usePrimary ? 'deepseek-reasoner' : 'gpt-3.5-turbo';

    try {
      aiLogger.info(`Calling ${usePrimary ? 'DeepSeek' : 'Backup'} AI API`);

      const response = await axios.post(
        `${apiUrl}/chat/completions`,
        {
          model,
          messages,
          temperature: 0.7,
          max_tokens: 2000,
          stream: false
        },
        {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      const result = response.data;
      
      aiLogger.info('AI API call successful', {
        model,
        usage: result.usage,
        service: usePrimary ? 'deepseek' : 'backup'
      });

      return {
        success: true,
        data: result.choices[0]?.message?.content,
        usage: result.usage
      };

    } catch (error: any) {
      aiLogger.error(`${usePrimary ? 'DeepSeek' : 'Backup'} AI API error:`, error.response?.data || error.message);
      
      // If primary service fails, try backup
      if (usePrimary && this.backupApiKey) {
        aiLogger.info('Falling back to backup AI service');
        return this.callAI(messages, false);
      }

      return {
        success: false,
        error: error.response?.data?.error?.message || error.message
      };
    }
  }

  /**
   * Analyze enterprise profile using AI
   */
  async analyzeEnterprise(request: EnterpriseAnalysisRequest): Promise<AIResponse> {
    const prompt = this.buildEnterpriseAnalysisPrompt(request);
    
    const messages = [
      {
        role: 'system',
        content: `你是一个专业的企业画像分析师，具有深厚的商业分析和市场洞察能力。
        你的任务是基于提供的企业信息，进行全面的企业画像分析，包括：
        1. 企业基本特征分析
        2. 市场定位和竞争优势
        3. 发展阶段和成长潜力
        4. 目标客户匹配度评估
        5. 商业价值和合作建议
        
        请以JSON格式返回分析结果，确保数据结构化且易于处理。`
      },
      {
        role: 'user',
        content: prompt
      }
    ];

    return this.callAI(messages);
  }

  /**
   * Generate intelligent matching recommendations
   */
  async generateMatchingRecommendations(enterprises: any[], targetConditions: any): Promise<AIResponse> {
    const prompt = `
    基于以下目标客户条件和企业列表，请进行智能匹配分析：
    
    目标条件：
    ${JSON.stringify(targetConditions, null, 2)}
    
    企业列表：
    ${JSON.stringify(enterprises, null, 2)}
    
    请分析每个企业与目标条件的匹配度，并提供：
    1. 匹配评分 (0-100)
    2. 匹配原因
    3. 潜在价值评估
    4. 接触建议
    5. 风险评估
    
    返回JSON格式的分析结果。
    `;

    const messages = [
      {
        role: 'system',
        content: '你是一个智能客户匹配专家，擅长分析企业特征并提供精准的匹配建议。'
      },
      {
        role: 'user',
        content: prompt
      }
    ];

    return this.callAI(messages);
  }

  /**
   * Analyze user decision pathway
   */
  async analyzeUserPathway(enterpriseData: any, roleData: any): Promise<AIResponse> {
    const prompt = `
    基于企业信息和角色数据，分析用户决策路径：
    
    企业信息：
    ${JSON.stringify(enterpriseData, null, 2)}
    
    角色信息：
    ${JSON.stringify(roleData, null, 2)}
    
    请分析：
    1. 决策流程各阶段
    2. 关键决策点
    3. 影响因素
    4. 转化瓶颈
    5. 优化建议
    
    返回结构化的分析结果。
    `;

    const messages = [
      {
        role: 'system',
        content: '你是一个用户行为分析专家，专注于B2B决策路径分析。'
      },
      {
        role: 'user',
        content: prompt
      }
    ];

    return this.callAI(messages);
  }

  private buildEnterpriseAnalysisPrompt(request: EnterpriseAnalysisRequest): string {
    return `
    请分析以下企业信息：
    
    企业名称：${request.companyName}
    所属行业：${request.industry || '未知'}
    企业描述：${request.description || '无'}
    
    目标客户条件：
    ${request.targetConditions ? JSON.stringify(request.targetConditions, null, 2) : '无特定条件'}
    
    请提供详细的企业画像分析，包括企业特征、市场地位、发展潜力等维度。
    `;
  }
}

export const aiService = new AIService();
