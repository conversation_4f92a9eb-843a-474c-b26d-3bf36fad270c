#!/usr/bin/env node

const http = require('http');

const API_BASE = 'http://localhost:3001';

// 测试API端点
const testEndpoints = [
  { name: '根路径', path: '/', method: 'GET' },
  { name: '健康检查', path: '/health', method: 'GET' },
  { name: 'AI状态', path: '/api/ai/status', method: 'GET' },
  { name: '企业列表', path: '/api/enterprises', method: 'GET' },
  { name: '企业统计', path: '/api/enterprises/stats/overview', method: 'GET' }
];

// AI分析测试数据
const aiTestData = {
  companyName: '腾讯科技',
  industry: '互联网',
  description: '中国领先的互联网增值服务提供商',
  targetConditions: {
    industry: '互联网',
    size: '1000+人',
    stage: '成长期'
  }
};

function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, API_BASE);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'API-Test-Script'
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(body);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: body
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testAPI() {
  console.log('🧪 企业画像师平台 API 测试');
  console.log('================================');
  console.log(`测试目标: ${API_BASE}`);
  console.log('');

  let passedTests = 0;
  let totalTests = 0;

  // 测试基础端点
  for (const endpoint of testEndpoints) {
    totalTests++;
    try {
      console.log(`📡 测试: ${endpoint.name} (${endpoint.method} ${endpoint.path})`);
      const result = await makeRequest(endpoint.path, endpoint.method);
      
      if (result.status >= 200 && result.status < 300) {
        console.log(`✅ 成功: ${result.status}`);
        if (result.data && typeof result.data === 'object') {
          console.log(`   响应: ${JSON.stringify(result.data).substring(0, 100)}...`);
        }
        passedTests++;
      } else {
        console.log(`❌ 失败: ${result.status}`);
        console.log(`   错误: ${JSON.stringify(result.data)}`);
      }
    } catch (error) {
      console.log(`❌ 连接失败: ${error.message}`);
    }
    console.log('');
  }

  // 测试AI分析功能
  totalTests++;
  try {
    console.log('🤖 测试: AI企业分析 (POST /api/ai/analyze-enterprise)');
    const result = await makeRequest('/api/ai/analyze-enterprise', 'POST', aiTestData);
    
    if (result.status >= 200 && result.status < 300) {
      console.log(`✅ 成功: ${result.status}`);
      console.log(`   AI分析响应: ${JSON.stringify(result.data).substring(0, 200)}...`);
      passedTests++;
    } else {
      console.log(`❌ 失败: ${result.status}`);
      console.log(`   错误: ${JSON.stringify(result.data)}`);
    }
  } catch (error) {
    console.log(`❌ 连接失败: ${error.message}`);
  }
  console.log('');

  // 测试结果总结
  console.log('📊 测试结果总结');
  console.log('================================');
  console.log(`通过测试: ${passedTests}/${totalTests}`);
  console.log(`成功率: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！后端服务运行正常。');
  } else {
    console.log('⚠️  部分测试失败，请检查后端服务状态。');
  }
  
  console.log('');
  console.log('💡 提示:');
  console.log('- 如果连接失败，请确认后端服务已启动 (npm run dev:server)');
  console.log('- 如果AI测试失败，请检查API密钥配置');
  console.log('- 查看详细日志: server/logs/combined.log');
}

// 运行测试
testAPI().catch(console.error);
