# 🔧 故障排除指南

## 常见问题解决方案

### 问题1: "路径 / 未找到" 错误

**症状：** 前端显示 `{"success":false,"error":{"code":"NOT_FOUND","message":"路径 / 未找到"}}`

**原因：** 后端服务未启动或前端无法连接到后端

**解决方案：**

1. **快速诊断**
   ```bash
   node diagnose.js
   ```

2. **快速修复**
   ```bash
   quick-fix.bat  # Windows
   # 或
   ./quick-fix.sh  # Linux/Mac
   ```

3. **手动检查步骤**
   ```bash
   # 1. 检查后端是否运行
   curl http://localhost:3001/health
   
   # 2. 如果失败，启动后端
   cd server
   npm run dev
   
   # 3. 在另一个终端启动前端
   npm run dev:client
   ```

### 问题2: 后端服务启动失败

**可能原因：**
- 端口3001被占用
- 依赖未安装
- 环境变量配置错误

**解决方案：**

1. **检查端口占用**
   ```bash
   # Windows
   netstat -ano | findstr :3001
   
   # Linux/Mac
   lsof -i :3001
   ```

2. **安装依赖**
   ```bash
   cd server
   npm install
   ```

3. **检查环境配置**
   ```bash
   # 确保存在 server/.env 文件
   cp server/.env.example server/.env
   ```

### 问题3: AI API调用失败

**症状：** AI功能无响应或返回错误

**解决方案：**

1. **检查API密钥**
   编辑 `server/.env` 文件，确认：
   ```
   DEEPSEEK_API_KEY=***********************************
   BACKUP_AI_API_KEY=sk-HZ5i3NCFjxNg81vDE178F940524b495a928715D450B7372e
   ```

2. **测试AI连接**
   ```bash
   curl -X GET http://localhost:3001/api/ai/status
   ```

3. **查看日志**
   ```bash
   tail -f server/logs/combined.log
   ```

### 问题4: 前端页面空白或加载失败

**解决方案：**

1. **清除缓存**
   ```bash
   # 删除 node_modules 重新安装
   rm -rf node_modules
   npm install
   ```

2. **检查Vite配置**
   确认 `vite.config.ts` 中的代理配置正确

3. **检查浏览器控制台**
   按F12查看控制台错误信息

## 🚀 完整重置流程

如果遇到复杂问题，可以执行完整重置：

```bash
# 1. 停止所有服务 (Ctrl+C)

# 2. 清理依赖
rm -rf node_modules
rm -rf server/node_modules

# 3. 重新安装
npm install
cd server && npm install && cd ..

# 4. 重置配置
cp server/.env.example server/.env

# 5. 启动服务
npm run dev
```

## 📊 系统状态检查

### 健康检查端点

- **后端健康检查**: http://localhost:3001/health
- **AI服务状态**: http://localhost:3001/api/ai/status
- **企业数据API**: http://localhost:3001/api/enterprises

### 预期响应

**健康检查成功响应：**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-08T...",
  "version": "1.0.0",
  "services": {
    "ai": "operational",
    "database": "connected"
  }
}
```

**AI状态成功响应：**
```json
{
  "success": true,
  "data": {
    "status": "operational",
    "services": {
      "deepseek": {
        "available": true,
        "model": "deepseek-reasoner"
      }
    }
  }
}
```

## 🔍 日志查看

### 后端日志位置
- **综合日志**: `server/logs/combined.log`
- **错误日志**: `server/logs/error.log`

### 查看实时日志
```bash
# Windows
type server\logs\combined.log

# Linux/Mac
tail -f server/logs/combined.log
```

## 📞 获取帮助

### 自动诊断工具
```bash
node diagnose.js
```

### API测试工具
```bash
node test-api.js
```

### 手动测试命令
```bash
# 测试后端连接
curl http://localhost:3001/health

# 测试企业API
curl http://localhost:3001/api/enterprises

# 测试AI分析
curl -X POST http://localhost:3001/api/ai/analyze-enterprise \
  -H "Content-Type: application/json" \
  -d '{"companyName":"测试公司","industry":"互联网"}'
```

## 🎯 开发环境要求

- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0
- **端口**: 3001 (后端), 5173 (前端)
- **网络**: 需要访问外部AI API

---

如果问题仍然存在，请查看项目日志文件或运行诊断工具获取更详细的错误信息。
