import { Router } from 'express';
import { asyncHandler } from '../middleware/errorHandler.js';
import { aiService } from '../services/aiService.js';
import { apiLogger } from '../utils/logger.js';
import Joi from 'joi';

const router = Router();

// Validation schemas
const enterpriseAnalysisSchema = Joi.object({
  companyName: Joi.string().required().min(1).max(200),
  industry: Joi.string().optional().max(100),
  description: Joi.string().optional().max(1000),
  targetConditions: Joi.object({
    industry: Joi.string().optional(),
    size: Joi.string().optional(),
    stage: Joi.string().optional(),
    location: Joi.string().optional()
  }).optional()
});

const matchingSchema = Joi.object({
  enterprises: Joi.array().items(Joi.object()).required(),
  targetConditions: Joi.object().required()
});

const pathwaySchema = Joi.object({
  enterpriseData: Joi.object().required(),
  roleData: Joi.object().required()
});

/**
 * @route POST /api/ai/analyze-enterprise
 * @desc Analyze enterprise profile using AI
 * @access Public (will add auth later)
 */
router.post('/analyze-enterprise', asyncHandler(async (req, res) => {
  apiLogger.info('Enterprise analysis request received', { body: req.body });

  // Validate request
  const { error, value } = enterpriseAnalysisSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      details: error.details
    });
  }

  // Call AI service
  const result = await aiService.analyzeEnterprise(value);

  if (!result.success) {
    return res.status(500).json({
      success: false,
      message: 'AI analysis failed',
      error: result.error
    });
  }

  res.json({
    success: true,
    data: {
      analysis: result.data,
      usage: result.usage,
      timestamp: new Date().toISOString()
    }
  });
}));

/**
 * @route POST /api/ai/generate-matching
 * @desc Generate intelligent matching recommendations
 * @access Public
 */
router.post('/generate-matching', asyncHandler(async (req, res) => {
  apiLogger.info('Matching generation request received');

  // Validate request
  const { error, value } = matchingSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      details: error.details
    });
  }

  // Call AI service
  const result = await aiService.generateMatchingRecommendations(
    value.enterprises,
    value.targetConditions
  );

  if (!result.success) {
    return res.status(500).json({
      success: false,
      message: 'Matching generation failed',
      error: result.error
    });
  }

  res.json({
    success: true,
    data: {
      recommendations: result.data,
      usage: result.usage,
      timestamp: new Date().toISOString()
    }
  });
}));

/**
 * @route POST /api/ai/analyze-pathway
 * @desc Analyze user decision pathway
 * @access Public
 */
router.post('/analyze-pathway', asyncHandler(async (req, res) => {
  apiLogger.info('Pathway analysis request received');

  // Validate request
  const { error, value } = pathwaySchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      details: error.details
    });
  }

  // Call AI service
  const result = await aiService.analyzeUserPathway(
    value.enterpriseData,
    value.roleData
  );

  if (!result.success) {
    return res.status(500).json({
      success: false,
      message: 'Pathway analysis failed',
      error: result.error
    });
  }

  res.json({
    success: true,
    data: {
      pathway: result.data,
      usage: result.usage,
      timestamp: new Date().toISOString()
    }
  });
}));

/**
 * @route GET /api/ai/status
 * @desc Check AI service status
 * @access Public
 */
router.get('/status', asyncHandler(async (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'operational',
      services: {
        deepseek: {
          available: !!process.env.DEEPSEEK_API_KEY,
          model: 'deepseek-reasoner'
        },
        backup: {
          available: !!process.env.BACKUP_AI_API_KEY,
          model: 'gpt-3.5-turbo'
        },
        tencent: {
          available: !!(process.env.TENCENT_SECRET_ID && process.env.TENCENT_SECRET_KEY),
          service: 'image-generation'
        }
      },
      timestamp: new Date().toISOString()
    }
  });
}));

export { router as aiRoutes };
