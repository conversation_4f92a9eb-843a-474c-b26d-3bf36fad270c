# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/enterprise_profiler
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=enterprise_analytics
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password

# AI API Configuration
# Primary AI Service - DeepSeek
DEEPSEEK_API_URL=https://api.deepseek.com
DEEPSEEK_API_KEY=***********************************
DEEPSEEK_MODEL=deepseek-reasoner

# Backup AI Service
BACKUP_AI_API_URL=https://vip.apiyi.com/v1
BACKUP_AI_API_KEY=sk-HZ5i3NCFjxNg81vDE178F940524b495a928715D450B7372e

# Tencent Cloud AI for Image Generation
TENCENT_SECRET_ID=AKIDynNhogIekaBWcR1LPEpAF4kCOJyNWP1N
TENCENT_SECRET_KEY=91dz5vUqwmMhphLktnRZRGyRdbKmytYC
TENCENT_REGION=ap-beijing

# Security
JWT_SECRET=your-super-secret-jwt-key-change-in-production
CORS_ORIGIN=http://localhost:5173

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log
