import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { errorHandler } from './middleware/errorHandler.js';
import { logger } from './utils/logger.js';
import { connectDatabases } from './config/database.js';
import { aiRoutes } from './routes/ai.js';
import { enterpriseRoutes } from './routes/enterprise.js';
import { analysisRoutes } from './routes/analysis.js';
import { visualizationRoutes } from './routes/visualization.js';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
  credentials: true
}));

// Logging middleware
app.use(morgan('combined', {
  stream: { write: (message) => logger.info(message.trim()) }
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: '企业画像师后端服务正在运行',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    mode: 'development',
    services: {
      ai: 'operational',
      database: 'ready',
      status: 'healthy'
    },
    project: 'enterprise-profiler'
  });
});

// API routes
app.use('/api/ai', aiRoutes);
app.use('/api/enterprises', enterpriseRoutes);
app.use('/api/analysis', analysisRoutes);
app.use('/api/visualization', visualizationRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found',
    path: req.originalUrl
  });
});

// Error handling middleware
app.use(errorHandler);

// Start server
async function startServer() {
  try {
    // TODO: Connect to databases in Phase 2
    // await connectDatabases();
    logger.info('📋 Database connections skipped for Phase 1 testing');

    app.listen(PORT, () => {
      logger.info(`🚀 Enterprise Profiler Server running on port ${PORT}`);
      logger.info(`📊 Environment: ${process.env.NODE_ENV}`);
      logger.info(`🔗 CORS enabled for: ${process.env.CORS_ORIGIN}`);
      logger.info(`🎯 Phase 1: AI-native backend architecture ready!`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

startServer();

export default app;
